package com.tmb.oneapp.paymentservice.service;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.oneapp.paymentservice.constant.ResponseCode;
import com.tmb.oneapp.paymentservice.model.CommonPaymentTransactionLog;
import com.tmb.oneapp.paymentservice.utils.PaymentUtils;
import feign.FeignException;
import io.github.resilience4j.circuitbreaker.CallNotPermittedException;
import io.github.resilience4j.circuitbreaker.CircuitBreaker;
import io.github.resilience4j.circuitbreaker.CircuitBreakerConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CommonPaymentTransactionLoggingServiceTest {

    @Mock
    private V1CommonPaymentTransactionLogService commonPaymentTransactionLogService;

    @InjectMocks
    private CommonPaymentTransactionLoggingService loggingService;

    private CommonPaymentTransactionLog transactionLog;

    @BeforeEach
    void setUp() {
        transactionLog = new CommonPaymentTransactionLog();
        transactionLog.setTransactionId("test-tx-id");
        transactionLog.setFinancialRefId("test-fin-ref");
    }

    @Test
    void testExecuteWithLogging_WhenBusinessLogicSucceeds_ShouldReturnResultAndLogSuccess() throws TMBCommonException {
        String successResult = "SUCCESS";
        transactionLog.setStatusCode("initial_success");
        CommonPaymentTransactionLoggingService.ThrowingSupplier<String> businessLogic = () -> successResult;

        String result = loggingService.executeWithLogging(transactionLog, businessLogic, null);

        assertEquals(successResult, result);

        ArgumentCaptor<CommonPaymentTransactionLog> captor = ArgumentCaptor.forClass(CommonPaymentTransactionLog.class);
        verify(commonPaymentTransactionLogService, times(1)).upsert(captor.capture());

        CommonPaymentTransactionLog capturedLog = captor.getValue();
        assertEquals("initial_success", capturedLog.getStatusCode());
    }

    @Test
    void testExecuteWithLogging_WhenFeignExceptionOccurs_ShouldThrowExceptionAndLogFailure() {
        FeignException feignException = mock(FeignException.class);
        TMBCommonException mappedException = new TMBCommonException("feign-error-code", "feign-error-message", "service", HttpStatus.INTERNAL_SERVER_ERROR, null);

        CommonPaymentTransactionLoggingService.ThrowingSupplier<Object> businessLogic = () -> {
            throw feignException;
        };

        TMBCommonException thrown = assertThrows(TMBCommonException.class, () -> {
            loggingService.executeWithLogging(transactionLog, businessLogic, e -> mappedException);
        });

        assertEquals(mappedException, thrown);

        ArgumentCaptor<CommonPaymentTransactionLog> captor = ArgumentCaptor.forClass(CommonPaymentTransactionLog.class);
        verify(commonPaymentTransactionLogService, times(1)).upsert(captor.capture());

        CommonPaymentTransactionLog capturedLog = captor.getValue();
        assertEquals("feign-error-code", capturedLog.getStatusCode());
        assertEquals("feign-error-message", capturedLog.getMessage());
    }

    @Test
    void testExecuteWithLogging_WhenCircuitBreakerOpens_ShouldThrowExceptionAndLogCircuitBreakerError() {
        CircuitBreakerConfig config = CircuitBreakerConfig.ofDefaults();
        CircuitBreaker circuitBreaker = mock(CircuitBreaker.class);
        when(circuitBreaker.getCircuitBreakerConfig()).thenReturn(config);
        CallNotPermittedException circuitBreakerException = CallNotPermittedException.createCallNotPermittedException(circuitBreaker);
        CommonPaymentTransactionLoggingService.ThrowingSupplier<Object> businessLogic = () -> {
            throw circuitBreakerException;
        };
        TMBCommonException expectedException = new TMBCommonException(ResponseCode.CIRCUIT_BREAK_ERROR.getCode(), "Circuit Breaker is open", "service", HttpStatus.OK, null);

        try (var mockedStatic = mockStatic(PaymentUtils.class)) {
            mockedStatic.when(() -> PaymentUtils.circuitBreakException(any())).thenReturn(expectedException);

            assertThrows(TMBCommonException.class, () -> {
                loggingService.executeWithLogging(transactionLog, businessLogic, null);
            });
        }

        ArgumentCaptor<CommonPaymentTransactionLog> captor = ArgumentCaptor.forClass(CommonPaymentTransactionLog.class);
        verify(commonPaymentTransactionLogService, times(1)).upsert(captor.capture());

        CommonPaymentTransactionLog capturedLog = captor.getValue();
        assertEquals(ResponseCode.CIRCUIT_BREAK_ERROR.getCode(), capturedLog.getStatusCode());
        assertNotNull(capturedLog.getMessage());
    }

    @Test
    void testExecuteWithLogging_WhenOtherExceptionOccurs_ShouldThrowAndLogInitialStatus() {
        transactionLog.setStatusCode("initial_success");
        IllegalArgumentException otherException = new IllegalArgumentException("Some other error");
        CommonPaymentTransactionLoggingService.ThrowingSupplier<Object> businessLogic = () -> {
            throw otherException;
        };

        assertThrows(IllegalArgumentException.class, () -> {
            loggingService.executeWithLogging(transactionLog, businessLogic, null);
        });

        ArgumentCaptor<CommonPaymentTransactionLog> captor = ArgumentCaptor.forClass(CommonPaymentTransactionLog.class);
        verify(commonPaymentTransactionLogService, times(1)).upsert(captor.capture());

        CommonPaymentTransactionLog capturedLog = captor.getValue();
        assertEquals("initial_success", capturedLog.getStatusCode());
    }
}
