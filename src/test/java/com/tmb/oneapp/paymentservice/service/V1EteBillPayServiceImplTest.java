package com.tmb.oneapp.paymentservice.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.paymentservice.client.BillPaymentAutoLoanConfirmFeignClient;
import com.tmb.oneapp.paymentservice.client.BillPaymentAutoLoanVerifyFeignClient;
import com.tmb.oneapp.paymentservice.client.OCPGatewayFeignClient;
import com.tmb.oneapp.paymentservice.constant.ResponseCode;
import com.tmb.oneapp.paymentservice.model.ete.ETEError;
import com.tmb.oneapp.paymentservice.model.ete.OCPBillPayment;
import com.tmb.oneapp.paymentservice.model.ete.OCPBillPaymentResponse;
import com.tmb.oneapp.paymentservice.model.ete.TopUpETEPaymentRequest;
import com.tmb.oneapp.paymentservice.model.ete.TopUpETEResponse;
import com.tmb.oneapp.paymentservice.model.ete.TopUpETETransaction;
import feign.FeignException;
import feign.Request;
import feign.RequestTemplate;
import feign.Response;
import io.github.resilience4j.circuitbreaker.CallNotPermittedException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;

import static com.tmb.oneapp.paymentservice.constant.PaymentServiceConstant.BILL_COMP_AIS_TMB_EXCEPTION_PREFIX;
import static com.tmb.oneapp.paymentservice.constant.PaymentServiceConstant.BILL_COMP_CODE_AIS_FIBER;
import static com.tmb.oneapp.paymentservice.constant.PaymentServiceConstant.BILL_COMP_CODE_TRUE_BILL;
import static com.tmb.oneapp.paymentservice.constant.PaymentServiceConstant.CONTENT_TYPE;
import static com.tmb.oneapp.paymentservice.constant.PaymentServiceConstant.HEADER_APP_ID;
import static com.tmb.oneapp.paymentservice.constant.PaymentServiceConstant.HEADER_CHANNEL;
import static com.tmb.oneapp.paymentservice.constant.PaymentServiceConstant.HEADER_REQUEST_UUID;
import static com.tmb.oneapp.paymentservice.constant.PaymentServiceConstant.PAYMENT_SERVICE;
import static com.tmb.oneapp.paymentservice.constant.PaymentServiceConstant.REQUEST_APP_ID_VALUE;
import static com.tmb.oneapp.paymentservice.constant.PaymentServiceConstant.REQUEST_DATE_TIME;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class V1EteBillPayServiceImplTest {
    @Mock
    private OCPGatewayFeignClient ocpGatewayFeignClient;
    @Mock
    private BillPaymentAutoLoanVerifyFeignClient billPaymentAutoLoanVerifyFeignClient;
    @Mock
    private BillPaymentAutoLoanConfirmFeignClient billPaymentAutoLoanConfirmFeignClient;
    @Mock
    private CommonPaymentTransactionLoggingService commonPaymentTransactionLoggingService;

    private V1EteBillPayServiceImpl v1EteBillPayServiceImpl;

    private OCPBillPayment ocpBillPayment;
    private OCPBillPaymentResponse ocpBillPaymentResponse;
    private TopUpETEPaymentRequest topUpETEPaymentRequest;
    private TopUpETEResponse topUpETEResponse;
    private static final String REQUEST_ID = "test-request-id";
    private static final String CORRELATION_ID = "test-correlation-id";

    @BeforeEach
    void setUp() throws TMBCommonException {
        v1EteBillPayServiceImpl = new V1EteBillPayServiceImpl(ocpGatewayFeignClient, billPaymentAutoLoanVerifyFeignClient, billPaymentAutoLoanConfirmFeignClient, commonPaymentTransactionLoggingService);
        when(commonPaymentTransactionLoggingService.executeWithLogging(any(), any(), any())).thenAnswer(invocation -> {
            CommonPaymentTransactionLoggingService.ThrowingSupplier<?> supplier = invocation.getArgument(1);
            java.util.function.Function<FeignException, TMBCommonException> feignExceptionHandler = invocation.getArgument(2);
            try {
                return supplier.get();
            } catch (FeignException e) {
                throw feignExceptionHandler.apply(e);
            } catch (CallNotPermittedException ce) {
                throw com.tmb.oneapp.paymentservice.utils.PaymentUtils.circuitBreakException(ResponseCode.CIRCUIT_BREAK_ERROR);
            }
        });
        ocpBillPayment = new OCPBillPayment();
        ocpBillPayment.setRequestId(REQUEST_ID);
        ocpBillPayment.setCompCode("AIS");

        ocpBillPaymentResponse = new OCPBillPaymentResponse();
        ocpBillPaymentResponse.setData(ocpBillPayment);

        topUpETEPaymentRequest = new TopUpETEPaymentRequest();
        topUpETEPaymentRequest.setTransactionId(REQUEST_ID);
        topUpETEPaymentRequest.setCompCode("AIS");

        TopUpETETransaction transaction = new TopUpETETransaction();
        transaction.setBankReferenceId(REQUEST_ID);

        topUpETEResponse = new TopUpETEResponse();
        topUpETEResponse.setTransaction(transaction);
    }

    @Test
    void testValidateOCPBillPaymentWhenSuccessShouldNotThrowsException() throws TMBCommonException {
        when(ocpGatewayFeignClient.billPaymentVerify(any(HttpHeaders.class), any(OCPBillPayment.class)))
                .thenReturn(ResponseEntity.ok(ocpBillPaymentResponse));

        OCPBillPaymentResponse result = v1EteBillPayServiceImpl.validateOCPBillPayment(ocpBillPayment);

        assertNotNull(result);
        assertEquals(REQUEST_ID, result.getData().getRequestId());
        verify(ocpGatewayFeignClient).billPaymentVerify(any(HttpHeaders.class), eq(ocpBillPayment));
    }

    @Test
    void testValidateOCPBillPaymentWhenResponseDataNullShouldDoesNotThrows() {
        ocpBillPaymentResponse = new OCPBillPaymentResponse();
        ocpBillPaymentResponse.setErrors(null);
        ocpBillPaymentResponse.setData(null);
        when(ocpGatewayFeignClient.billPaymentVerify(any(HttpHeaders.class), any(OCPBillPayment.class)))
                .thenReturn(ResponseEntity.ok(ocpBillPaymentResponse));

        assertDoesNotThrow(() -> v1EteBillPayServiceImpl.validateOCPBillPayment(ocpBillPayment));
    }

    @Test
    void testValidateOCPBillPaymentWhenFailedGotETEExceptionWithHttp200ShouldThrowsTmbCommonException() {
        OCPBillPaymentResponse errorResponse = new OCPBillPaymentResponse();
        ETEError eteError = new ETEError();
        eteError.setNamespace("XES");
        eteError.setCode("E-001");
        eteError.setMessage("Test error");
        errorResponse.setErrors(List.of(
                new ETEError("ignore", "ignore", "ignore"),
                new ETEError("ignore", "ignore", "ignore"),
                eteError
        ));
        when(ocpGatewayFeignClient.billPaymentVerify(any(HttpHeaders.class), any(OCPBillPayment.class)))
                .thenReturn(ResponseEntity.ok(errorResponse));

        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> v1EteBillPayServiceImpl.validateOCPBillPayment(ocpBillPayment));

        assertEquals("cbs_e001", exception.getErrorCode());
    }

    @Test
    void testValidateOCPBillPaymentWhenFailedGotFeignExceptionShouldThrowsTmbCommonException() throws JsonProcessingException {
        OCPBillPaymentResponse errorResponse = new OCPBillPaymentResponse();
        ETEError eteError = new ETEError();
        eteError.setNamespace("XES");
        eteError.setCode("E-001");
        eteError.setMessage("Test error");
        errorResponse.setErrors(List.of(eteError));
        when(ocpGatewayFeignClient.billPaymentVerify(any(HttpHeaders.class), any(OCPBillPayment.class)))
                .thenThrow(createFeignException(500, errorResponse));

        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> v1EteBillPayServiceImpl.validateOCPBillPayment(ocpBillPayment));

        assertEquals("cbs_e001", exception.getErrorCode());
    }

    @Test
    void testValidateOCPBillPaymentWhenGotCircuitBreakerException() {
        when(ocpGatewayFeignClient.billPaymentVerify(any(HttpHeaders.class), any(OCPBillPayment.class)))
                .thenThrow(CallNotPermittedException.class);

        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> v1EteBillPayServiceImpl.validateOCPBillPayment(ocpBillPayment));
        assertEquals(ResponseCode.CIRCUIT_BREAK_ERROR.getCode(), exception.getErrorCode());
    }

    @Test
    void testConfirmOCPBillPaymentWhenSuccessShouldNotThrowsException() throws TMBCommonException {
        when(ocpGatewayFeignClient.billPaymentConfirm(any(HttpHeaders.class), any(OCPBillPayment.class)))
                .thenReturn(ResponseEntity.ok(ocpBillPaymentResponse));

        OCPBillPaymentResponse result = v1EteBillPayServiceImpl.confirmOCPBillPayment(ocpBillPayment);

        assertNotNull(result);
        assertEquals(REQUEST_ID, result.getData().getRequestId());
        verify(ocpGatewayFeignClient).billPaymentConfirm(any(HttpHeaders.class), eq(ocpBillPayment));
    }

    @Test
    void testConfirmOCPBillPaymentWhenFailedGotResponseDataNullShouldDoesNotThrows() throws TMBCommonException {
        ocpBillPaymentResponse = new OCPBillPaymentResponse();
        ocpBillPaymentResponse.setErrors(null);
        ocpBillPaymentResponse.setData(null);
        when(ocpGatewayFeignClient.billPaymentConfirm(any(HttpHeaders.class), any(OCPBillPayment.class)))
                .thenReturn(ResponseEntity.ok(ocpBillPaymentResponse));

        v1EteBillPayServiceImpl.confirmOCPBillPayment(ocpBillPayment);
    }

    @Test
    void testConfirmOCPBillPaymentWhenFailedGotETEExceptionWithHttp200WithErrorNamespaceXESShouldThrowsTmbCommonException() {
        OCPBillPaymentResponse errorResponse = new OCPBillPaymentResponse();
        ETEError eteError = new ETEError();
        eteError.setNamespace("XES");
        eteError.setCode("E-001");
        eteError.setMessage("Test error");
        errorResponse.setErrors(List.of(eteError));
        when(ocpGatewayFeignClient.billPaymentConfirm(any(HttpHeaders.class), any(OCPBillPayment.class)))
                .thenReturn(ResponseEntity.ok(errorResponse));

        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> v1EteBillPayServiceImpl.confirmOCPBillPayment(ocpBillPayment));

        assertEquals("cbs_e001", exception.getErrorCode());
    }

    @Test
    void testConfirmOCPBillPaymentWhenFailedGotFeignExceptionWithErrorNamespaceXESShouldThrowsTmbCommonException() throws JsonProcessingException {
        OCPBillPaymentResponse errorResponse = new OCPBillPaymentResponse();
        ETEError eteError = new ETEError();
        eteError.setNamespace("XES");
        eteError.setCode("E-001");
        eteError.setMessage("Test error");
        errorResponse.setErrors(List.of(eteError));
        when(ocpGatewayFeignClient.billPaymentConfirm(any(HttpHeaders.class), any(OCPBillPayment.class)))
                .thenThrow(createFeignException(500, errorResponse));

        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> v1EteBillPayServiceImpl.confirmOCPBillPayment(ocpBillPayment));

        assertEquals("cbs_e001", exception.getErrorCode());
    }

    @Test
    void testConfirmOCPBillPaymentWhenGotCircuitBreakerExceptionShouldThrowsTmbCommonException() {
        when(ocpGatewayFeignClient.billPaymentConfirm(any(HttpHeaders.class), any(OCPBillPayment.class)))
                .thenThrow(CallNotPermittedException.class);

        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> v1EteBillPayServiceImpl.confirmOCPBillPayment(ocpBillPayment));
        assertEquals(ResponseCode.CIRCUIT_BREAK_ERROR.getCode(), exception.getErrorCode());
    }

    @Test
    void testExtractErrorsListWhenErrorObjectAreDifferenceShouldThrowTMBCommonException() throws IOException {
        String json = "{\n" +
                "    \"errors\": \"incorrect-error-structure\"" +
                "}";
        when(ocpGatewayFeignClient.billPaymentVerify(any(HttpHeaders.class), any(OCPBillPayment.class)))
                .thenThrow(createFeignException(500, TMBUtils.convertStringToJavaObj(json, Object.class)));


        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> v1EteBillPayServiceImpl.validateOCPBillPayment(ocpBillPayment));

        assertEquals(ResponseCode.FAILED_V2.getCode(), exception.getErrorCode());
    }

    private FeignException createFeignException(int status, Object object) throws JsonProcessingException {
        String errorMessage = TMBUtils.convertJavaObjectToString(object);

        Request request = Request.create(Request.HttpMethod.POST, "url",
                new HashMap<>(), null, new RequestTemplate());

        Response response = Response.builder()
                .status(status)
                .request(request)
                .body(errorMessage.getBytes(StandardCharsets.UTF_8))
                .build();

        return FeignException.errorStatus("error", response);
    }

    @Test
    void testValidateAutoLoanBillPaymentWhenSuccessShouldNotThrowsException() throws TMBCommonException {
        when(billPaymentAutoLoanVerifyFeignClient.billPaymentAutoLoanVerify(any(HttpHeaders.class), any(TopUpETEPaymentRequest.class)))
                .thenReturn(ResponseEntity.ok(topUpETEResponse));

        TopUpETEResponse result = v1EteBillPayServiceImpl.validateAutoLoanBillPayment(topUpETEPaymentRequest);

        assertNotNull(result);
        assertNotNull(result.getTransaction());
        assertEquals(REQUEST_ID, result.getTransaction().getBankReferenceId());
        verify(billPaymentAutoLoanVerifyFeignClient).billPaymentAutoLoanVerify(any(HttpHeaders.class), eq(topUpETEPaymentRequest));
    }

    @Test
    void testValidateAutoLoanBillPaymentWhenResponseDataNullShouldDoesNotThrows() {
        topUpETEResponse = new TopUpETEResponse();
        topUpETEResponse.setErrors(null);
        topUpETEResponse.setTransaction(null);
        when(billPaymentAutoLoanVerifyFeignClient.billPaymentAutoLoanVerify(any(HttpHeaders.class), any(TopUpETEPaymentRequest.class)))
                .thenReturn(ResponseEntity.ok(topUpETEResponse));

        assertDoesNotThrow(() -> v1EteBillPayServiceImpl.validateAutoLoanBillPayment(topUpETEPaymentRequest));
    }

    @Test
    void testValidateAutoLoanBillPaymentWhenFailedGotETEExceptionWithHttp200ShouldThrowsTmbCommonException() {
        TopUpETEResponse errorResponse = new TopUpETEResponse();
        ETEError eteError = new ETEError();
        eteError.setNamespace("XES");
        eteError.setCode("E-001");
        eteError.setMessage("Test error");
        errorResponse.setErrors(List.of(
                new ETEError("ignore", "ignore", "ignore"),
                new ETEError("ignore", "ignore", "ignore"),
                eteError
        ));
        when(billPaymentAutoLoanVerifyFeignClient.billPaymentAutoLoanVerify(any(HttpHeaders.class), any(TopUpETEPaymentRequest.class)))
                .thenReturn(ResponseEntity.ok(errorResponse));

        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> v1EteBillPayServiceImpl.validateAutoLoanBillPayment(topUpETEPaymentRequest));

        assertEquals("cbs_e001", exception.getErrorCode());
    }

    @Test
    void testValidateAutoLoanBillPaymentWhenFailedGotFeignExceptionShouldThrowsTmbCommonException() throws JsonProcessingException {
        OCPBillPaymentResponse errorResponse = new OCPBillPaymentResponse();
        ETEError eteError = new ETEError();
        eteError.setNamespace("XES");
        eteError.setCode("E-001");
        eteError.setMessage("Test error");
        errorResponse.setErrors(List.of(eteError));
        when(billPaymentAutoLoanVerifyFeignClient.billPaymentAutoLoanVerify(any(HttpHeaders.class), any(TopUpETEPaymentRequest.class)))
                .thenThrow(createFeignException(500, errorResponse));

        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> v1EteBillPayServiceImpl.validateAutoLoanBillPayment(topUpETEPaymentRequest));

        assertEquals("cbs_e001", exception.getErrorCode());
    }

    @Test
    void testValidateAutoLoanBillPaymentWhenGotCircuitBreakerException() {
        when(billPaymentAutoLoanVerifyFeignClient.billPaymentAutoLoanVerify(any(HttpHeaders.class), any(TopUpETEPaymentRequest.class)))
                .thenThrow(CallNotPermittedException.class);

        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> v1EteBillPayServiceImpl.validateAutoLoanBillPayment(topUpETEPaymentRequest));
        assertEquals(ResponseCode.CIRCUIT_BREAK_ERROR.getCode(), exception.getErrorCode());
    }

    @Test
    void testConfirmWowPointPaymentWhenSuccessShouldNotThrowsException() throws TMBCommonException {
        when(ocpGatewayFeignClient.billPaymentPointConfirm(any(HttpHeaders.class), any(OCPBillPayment.class)))
                .thenReturn(ResponseEntity.ok(ocpBillPaymentResponse));

        OCPBillPaymentResponse result = v1EteBillPayServiceImpl.confirmWowPointPayment(ocpBillPayment);

        assertNotNull(result);
        assertEquals(REQUEST_ID, result.getData().getRequestId());
        verify(ocpGatewayFeignClient).billPaymentPointConfirm(any(HttpHeaders.class), any(OCPBillPayment.class));
    }

    @Test
    void testConfirmWowPointPaymentWhenFailedGot200ETEExceptionShouldThrowsTmbCommonException() {
        OCPBillPaymentResponse errorResponse = new OCPBillPaymentResponse();
        ETEError eteError = new ETEError();
        eteError.setNamespace("XES");
        eteError.setCode("E-001");
        eteError.setMessage("Test error");
        errorResponse.setErrors(List.of(
                new ETEError("ignore", "ignore", "ignore"),
                new ETEError("ignore", "ignore", "ignore"),
                new ETEError("ignore", "ignore", "ignore"),
                eteError
        ));
        when(ocpGatewayFeignClient.billPaymentPointConfirm(any(HttpHeaders.class), any(OCPBillPayment.class)))
                .thenReturn(ResponseEntity.ok(errorResponse));

        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> v1EteBillPayServiceImpl.confirmWowPointPayment(ocpBillPayment));

        assertEquals("cbs_e001", exception.getErrorCode());
        assertEquals("Test error", exception.getErrorMessage());
    }

    @Test
    void testConfirmWowPointPaymentWhenFailedGot400ETEExceptionShouldThrowsTmbCommonException() throws JsonProcessingException {
        String json = """
                {
                    "errors": [{
                        "code": "4102",
                        "namespace": "BAPI",
                        "message": "Please provide a ref4"
                    }]
                }
                """;
        when(ocpGatewayFeignClient.billPaymentPointConfirm(any(HttpHeaders.class), any(OCPBillPayment.class)))
                .thenThrow(createFeignException(400, TMBUtils.convertStringToJavaObj(json, Object.class)));

        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> v1EteBillPayServiceImpl.confirmWowPointPayment(ocpBillPayment));

        assertEquals("bapi_4102", exception.getErrorCode());
        assertEquals("Please provide a ref4", exception.getErrorMessage());
    }

    @Test
    void testConfirmWowPointPaymentWhenFailedGot400ETEExceptionWithWowPointShouldThrowsTmbCommonException() throws JsonProcessingException {
        String json = """
                {
                    "errors": [{
                        "code": "0059",
                        "namespace": "WOW",
                        "message": "serial code is invalid"
                    }]
                }
                """;
        when(ocpGatewayFeignClient.billPaymentPointConfirm(any(HttpHeaders.class), any(OCPBillPayment.class)))
                .thenThrow(createFeignException(400, TMBUtils.convertStringToJavaObj(json, Object.class)));

        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> v1EteBillPayServiceImpl.confirmWowPointPayment(ocpBillPayment));

        assertEquals("wow_0059", exception.getErrorCode());
        assertEquals("serial code is invalid", exception.getErrorMessage());
    }

    @Test
    void testConfirmWowPointPaymentWhenFailedGot500ETEExceptionShouldThrowsTmbCommonException() throws JsonProcessingException {
        String json = """
                {
                    "errors": [{
                        "code": "8891",
                        "namespace": "XES",
                        "message": "INVALID AMOUNT"
                    }]
                }
                """;
        when(ocpGatewayFeignClient.billPaymentPointConfirm(any(HttpHeaders.class), any(OCPBillPayment.class)))
                .thenThrow(createFeignException(500, TMBUtils.convertStringToJavaObj(json, Object.class)));

        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> v1EteBillPayServiceImpl.confirmWowPointPayment(ocpBillPayment));

        assertEquals("cbs_8891", exception.getErrorCode());
        assertEquals("INVALID AMOUNT", exception.getErrorMessage());
    }

    @Test
    void testConfirmWowPontPaymentWhenGotCircuitBreakerExceptionShouldThrowsTmbCommonException() {
        when(ocpGatewayFeignClient.billPaymentPointConfirm(any(HttpHeaders.class), any(OCPBillPayment.class)))
                .thenThrow(CallNotPermittedException.class);

        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> v1EteBillPayServiceImpl.confirmWowPointPayment(ocpBillPayment));
        assertEquals(ResponseCode.CIRCUIT_BREAK_ERROR.getCode(), exception.getErrorCode());
    }

    @Test
    void testConfirmAutoLoanBillPaymentWhenSuccessShouldNotThrowsException() throws TMBCommonException {
        when(billPaymentAutoLoanConfirmFeignClient.billPaymentAutoLoanConfirm(any(HttpHeaders.class), any(TopUpETEPaymentRequest.class)))
                .thenReturn(ResponseEntity.ok(topUpETEResponse));

        TopUpETEResponse result = v1EteBillPayServiceImpl.confirmAutoLoanBillPayment(topUpETEPaymentRequest);

        assertNotNull(result);
        assertNotNull(result.getTransaction());
        assertEquals(REQUEST_ID, result.getTransaction().getBankReferenceId());
        verify(billPaymentAutoLoanConfirmFeignClient).billPaymentAutoLoanConfirm(any(HttpHeaders.class), eq(topUpETEPaymentRequest));
    }

    @Test
    void testConfirmAutoLoanBillPaymentWhenResponseDataNullShouldDoesNotThrows() throws TMBCommonException {
        topUpETEResponse = new TopUpETEResponse();
        topUpETEResponse.setErrors(null);
        topUpETEResponse.setTransaction(null);
        when(billPaymentAutoLoanConfirmFeignClient.billPaymentAutoLoanConfirm(any(HttpHeaders.class), any(TopUpETEPaymentRequest.class)))
                .thenReturn(ResponseEntity.ok(topUpETEResponse));

        v1EteBillPayServiceImpl.confirmAutoLoanBillPayment(topUpETEPaymentRequest);
    }

    @Test
    void testConfirmAutoLoanBillPaymentWhenFailedGotETEExceptionWithHttp200ShouldThrowsTmbCommonException() {
        TopUpETEResponse errorResponse = new TopUpETEResponse();
        ETEError eteError = new ETEError();
        eteError.setNamespace("XES");
        eteError.setCode("E-001");
        eteError.setMessage("Test error");
        errorResponse.setErrors(List.of(
                new ETEError("ignore", "ignore", "ignore"),
                new ETEError("ignore", "ignore", "ignore"),
                new ETEError("ignore", "ignore", "ignore"),
                eteError
        ));
        when(billPaymentAutoLoanConfirmFeignClient.billPaymentAutoLoanConfirm(any(HttpHeaders.class), any(TopUpETEPaymentRequest.class)))
                .thenReturn(ResponseEntity.ok(errorResponse));

        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> v1EteBillPayServiceImpl.confirmAutoLoanBillPayment(topUpETEPaymentRequest));

        assertEquals("cbs_e001", exception.getErrorCode());
    }

    @Test
    void testConfirmAutoLoanBillPaymentWhenFailedGotETEExceptionWithHttp200AndBillerGroupTypeTrueShouldThrowsTmbCommonException() {
        topUpETEPaymentRequest.setCompCode(BILL_COMP_CODE_TRUE_BILL);
        TopUpETEResponse errorResponse = new TopUpETEResponse();
        ETEError eteError = new ETEError();
        eteError.setCode("W001");
        eteError.setNamespace("true");
        eteError.setMessage("Test error");
        errorResponse.setErrors(List.of(
                new ETEError("ignore", "ignore", "ignore"),
                new ETEError("ignore", "ignore", "ignore"),
                new ETEError("ignore", "ignore", "ignore"),
                eteError
        ));
        when(billPaymentAutoLoanConfirmFeignClient.billPaymentAutoLoanConfirm(any(HttpHeaders.class), any(TopUpETEPaymentRequest.class)))
                .thenReturn(ResponseEntity.ok(errorResponse));

        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> v1EteBillPayServiceImpl.confirmAutoLoanBillPayment(topUpETEPaymentRequest));

        assertEquals("true_w001", exception.getErrorCode());
    }

    @Test
    void testConfirmAutoLoanBillPaymentWhenFailedGotETEExceptionWithHttp200AndBillerGroupAisShouldThrowsTmbCommonException() {
        topUpETEPaymentRequest.setCompCode(BILL_COMP_CODE_AIS_FIBER);

        TopUpETEResponse errorResponse = new TopUpETEResponse();
        ETEError eteError = new ETEError();
        eteError.setCode("E-001");
        eteError.setNamespace("ignore");
        eteError.setMessage("Test error");
        errorResponse.setErrors(List.of(
                new ETEError("ignore", "ignore", "ignore"),
                new ETEError("ignore", "ignore", "ignore"),
                new ETEError("ignore", "ignore", "ignore"),
                eteError
        ));
        when(billPaymentAutoLoanConfirmFeignClient.billPaymentAutoLoanConfirm(any(HttpHeaders.class), any(TopUpETEPaymentRequest.class)))
                .thenReturn(ResponseEntity.ok(errorResponse));

        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> v1EteBillPayServiceImpl.confirmAutoLoanBillPayment(topUpETEPaymentRequest));

        String expectErrorCode = BILL_COMP_AIS_TMB_EXCEPTION_PREFIX.toLowerCase() + "e001";
        assertEquals(expectErrorCode, exception.getErrorCode());
    }

    @Test
    void testConfirmAutoLoanBillPaymentWhenFailedGotFeignExceptionShouldThrowsTmbCommonException() throws JsonProcessingException {
        OCPBillPaymentResponse errorResponse = new OCPBillPaymentResponse();
        ETEError eteError = new ETEError();
        eteError.setNamespace("XES");
        eteError.setCode("E-001");
        eteError.setMessage("Test error");
        errorResponse.setErrors(List.of(eteError));
        when(billPaymentAutoLoanConfirmFeignClient.billPaymentAutoLoanConfirm(any(HttpHeaders.class), any(TopUpETEPaymentRequest.class)))
                .thenThrow(createFeignException(500, errorResponse));

        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> v1EteBillPayServiceImpl.confirmAutoLoanBillPayment(topUpETEPaymentRequest));

        assertEquals("cbs_e001", exception.getErrorCode());
    }

    @Test
    void testConfirmAutoLoanBillPaymentWhenGotCircuitBreakerExceptionShouldThrowsTmbCommonException() {
        when(billPaymentAutoLoanConfirmFeignClient.billPaymentAutoLoanConfirm(any(HttpHeaders.class), any(TopUpETEPaymentRequest.class)))
                .thenThrow(CallNotPermittedException.class);

        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> v1EteBillPayServiceImpl.confirmAutoLoanBillPayment(topUpETEPaymentRequest));
        assertEquals(ResponseCode.CIRCUIT_BREAK_ERROR.getCode(), exception.getErrorCode());
    }

    @Test
    void testSetUpRequestHeaderShouldSetAllRequiredHeaders() {
        String testServiceName = "test-service";

        HttpHeaders headers = v1EteBillPayServiceImpl.setUpRequestHeader(testServiceName);

        assertNotNull(headers);
        assertNotNull(headers.get(HEADER_REQUEST_UUID));
        assertEquals(REQUEST_APP_ID_VALUE, headers.getFirst(HEADER_APP_ID));
        assertNotNull(headers.getFirst(REQUEST_DATE_TIME));
        assertEquals("application/json;charset=UTF-8", headers.getFirst(CONTENT_TYPE));
        assertEquals(testServiceName, headers.getFirst(PAYMENT_SERVICE));
        assertEquals("1MB", headers.getFirst(HEADER_CHANNEL));
    }

    @Test
    void testExtractErrorsListWhenSuccessShouldReturnErrorsList() throws TMBCommonException {
        String json = "{"
                + "    \"errors\": [{"
                + "        \"namespace\": \"XES\","
                + "        \"code\": \"E-001\","
                + "        \"message\": \"Test error\""
                + "    }]"
                + "}";

        List<ETEError> errors = v1EteBillPayServiceImpl.extractErrorsList(json);

        assertNotNull(errors);
        assertEquals(1, errors.size());
        ETEError error = errors.get(0);
        assertEquals("XES", error.getNamespace());
        assertEquals("E-001", error.getCode());
        assertEquals("Test error", error.getMessage());
    }
}
